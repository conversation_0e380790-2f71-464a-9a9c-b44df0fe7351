<template>
  <Transition name="chat-dialog-fade" appear @after-leave="handleAfterLeave">
    <div v-if="visible" class="chat-dialog-overlay" @click="handleOverlayClick">
      <div class="chat-dialog" :class="{ show: visible }" @click.stop>
        <!-- 对话头部 -->
        <div class="chat-header">
          <div class="header-left" @click="handleHeaderClick">
            <div class="header-indicator"></div>
            <div class="header-arrow">
              <div class="arrow-down"></div>
            </div>
          </div>
          <div class="header-right">
            <button v-if="messages.length > 0" class="new-chat-button" title="开始新对话" @click="handleNewChat">
              <span class="plus-icon">+</span>
            </button>
          </div>
        </div>

        <!-- 对话消息区域 -->
        <div ref="chatMessagesRef" class="chat-messages">
          <template v-for="(message, index) in messages" :key="message.key">
            <ChatItem
              :message-data="message"
              :is-regenerate="index === messages.length - 1"
              @regenerate="handleRegenerate"
            />
          </template>

          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="empty-state">
            <div class="empty-icon">💬</div>
            <div class="empty-text">开始对话吧</div>
          </div>
        </div>

        <!-- 输入框区域 -->
        <div class="chat-input-area">
          <div class="input-container">
            <textarea
              v-model="inputMessage"
              placeholder="可以问我任何问题"
              class="chat-input"
              @keydown.enter.prevent="handleSendMessage"
              @input="handleInputChange"
            ></textarea>
            <button
              class="send-button"
              :class="{ disabled: !inputMessage.trim() }"
              :disabled="!inputMessage.trim()"
              @click="handleSendMessage"
            >
              <i class="iconfont icon-mobile-send"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import ChatItem from '@/pages/Chat/components/chatItem.vue';

// Props
interface IProps {
  visible: boolean;
  messages: IChatStreamContent[];
  conversationId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits
const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'send-message', message: string): void;
  (e: 'regenerate', messageData: IChatStreamContent): void;
  (e: 'new-chat'): void;
}>();

// Refs
const chatMessagesRef = ref<HTMLElement>();
const inputMessage = ref('');

// 处理覆盖层点击（点击上半部分收起）
const handleOverlayClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  const dialogElement = target.closest('.chat-dialog');

  // 如果点击的不是对话框内部，则关闭
  if (!dialogElement) {
    console.log('🔽 [ChatDialog] 点击覆盖层，收起对话框');
    emit('close');
  }
};

// 处理头部点击（收起功能）
const handleHeaderClick = () => {
  // 清空输入框
  inputMessage.value = '';
  emit('close');
};

// 处理重新生成
const handleRegenerate = (messageData: IChatStreamContent) => {
  emit('regenerate', messageData);
};

// 处理输入变化
const handleInputChange = () => {
  // 可以在这里添加输入验证逻辑
};

// 处理发送消息
const handleSendMessage = () => {
  const message = inputMessage.value.trim();
  if (!message) return;

  console.log('📤 [ChatDialog] 发送消息:', message);
  emit('send-message', message);

  // 清空输入框
  inputMessage.value = '';
};

// 处理新对话
const handleNewChat = () => {
  console.log('🆕 [ChatDialog] 开始新对话');
  // 清空输入框
  inputMessage.value = '';
  emit('new-chat');
};

// 处理过渡动画结束后的清理
const handleAfterLeave = () => {
  console.log('🧹 [ChatDialog] 对话框完全关闭，清理状态');
  // 确保输入框被清空
  inputMessage.value = '';
  // 确保body overflow样式被重置
  document.body.style.overflow = '';
  // 强制重新渲染，确保DOM完全清理
  void nextTick(() => {
    console.log('✅ [ChatDialog] DOM清理完成');
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTo({
      top: chatMessagesRef.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    void nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true },
);

// 监听显示状态变化，显示时滚动到底部
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      void nextTick(() => {
        scrollToBottom();
      });
    } else {
      // 对话框关闭时清空输入框
      inputMessage.value = '';
      // 确保body overflow样式被重置
      document.body.style.overflow = '';
    }
  },
);
</script>

<style lang="scss" scoped>
// 统一配色方案
:root {
  --primary-color: #00bcd4;
  --primary-color-light: rgba(0, 188, 212, 0.1);
  --primary-color-medium: rgba(0, 188, 212, 0.2);
  --primary-color-strong: rgba(0, 188, 212, 0.3);

  --accent-color: #00ffff;
  --accent-color-light: rgba(0, 255, 255, 0.1);
  --accent-color-medium: rgba(0, 255, 255, 0.2);
  --accent-color-strong: rgba(0, 255, 255, 0.3);

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.7);
  --text-disabled: rgba(255, 255, 255, 0.5);

  --bg-glass: rgba(30, 58, 138, 0.15);
  --bg-glass-hover: rgba(30, 58, 138, 0.25);
  --border-glass: rgba(255, 255, 255, 0.2);
  --border-accent: rgba(0, 255, 255, 0.3);

  --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.3);
  --shadow-accent: 0 0 20px rgba(0, 255, 255, 0.2);
}

.chat-dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1001;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
  pointer-events: auto;
}

.chat-dialog {
  width: 100%;
  max-width: 100vw;
  height: 50vh;
  background: rgba(1, 28, 32, 0.9);
  border: 2px solid var(--border-accent);
  border-radius: 20px 20px 0 0;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-strong), var(--shadow-accent);
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out forwards;

  &.show {
    animation: slideUp 0.3s ease-out forwards;
  }

  &.hide {
    animation: slideDown 0.3s ease-in forwards;
  }
}

.chat-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-glass);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 100px;

  .header-left {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
    flex: 1;
    position: relative;

    .header-indicator {
      width: 40px;
      height: 4px;
      background: var(--accent-color);
      border-radius: 2px;
      position: absolute;
      left: 50%;
      top: -8px;
      transform: translateX(-50%);
    }

    .header-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 8px;

      .arrow-down {
        width: 0;
        height: 0;
        border-left: 14px solid transparent;
        border-right: 14px solid transparent;
        border-top: 18px solid var(--text-primary);
        transition: transform 0.2s ease;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .new-chat-button {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 4px solid var(--border-accent);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(20px);

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--accent-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
      }

      .plus-icon {
        color: #ffffff;
        font-size: 24px;
        font-weight: 600;
        line-height: 1;
      }
    }
  }
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;

  // iOS Safari 滚动优化
  @supports (-webkit-touch-callout: none) {
    -webkit-overflow-scrolling: touch;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 400;
  }
}

.chat-input-area {
  padding: 28px 40px 36px;
  background: transparent;

  .input-container {
    display: flex;
    align-items: flex-end;
    gap: 16px;

    .chat-input {
      flex: 1;
      height: 68px;
      padding: 24px 20px;
      background: var(--bg-glass);
      border: 2px solid var(--border-accent);
      border-radius: var(--border-radius-xl);
      color: var(--text-primary);
      font-size: var(--font-size-2xl);
      font-weight: 500;
      line-height: 1.4;
      outline: none;
      resize: none;
      transition: all 0.2s ease;
      box-sizing: border-box;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong), var(--shadow-accent);

      &::placeholder {
        color: var(--text-quaternary);
        font-weight: 400;
      }

      &:focus {
        border-color: var(--accent-color);
        background: var(--bg-glass-hover);
        box-shadow:
          var(--shadow-strong),
          var(--shadow-accent),
          0 0 0 2px rgba(0, 255, 255, 0.1);
      }

      // iOS Safari 兼容性修复
      @supports (-webkit-touch-callout: none) {
        background: var(--bg-glass) !important;
        border-color: var(--border-accent) !important;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);

        &:focus {
          background: var(--bg-glass-hover) !important;
        }
      }
    }

    .send-button {
      width: 68px;
      height: 68px;
      border-radius: 50%;
      background: var(--primary-color-light);
      border: 2px solid var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      flex-shrink: 0;
      backdrop-filter: blur(20px);
      box-shadow: var(--shadow-strong);

      &:hover:not(.disabled) {
        background: var(--primary-color-medium);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow:
          var(--shadow-strong),
          0 8px 24px var(--primary-color-strong);
      }

      &.disabled {
        background: var(--text-disabled);
        border-color: var(--text-disabled);
        cursor: not-allowed;
        opacity: 0.5;
      }

      i {
        color: var(--primary-color);
        font-size: 56px;
      }
    }
  }
}

// Transition 动画
.chat-dialog-fade-enter-active,
.chat-dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}

.chat-dialog-fade-enter-from,
.chat-dialog-fade-leave-to {
  opacity: 0;
}

// 确保离开动画完成后元素完全移除
.chat-dialog-fade-leave-to {
  pointer-events: none;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .chat-dialog {
    height: 60vh;
    border-radius: 16px 16px 0 0;
  }

  .chat-header {
    padding: 12px 16px;

    .header-title {
      font-size: 16px;
    }
  }

  .chat-messages {
    padding: 16px;
    gap: 12px;
  }

  .chat-input-area {
    padding: 20px 20px 20px;

    .input-container {
      gap: 12px;

      .chat-input {
        height: 64px;
        padding: 20px 16px;
        font-size: var(--font-size-xl);
        border-radius: var(--border-radius-lg);
      }

      .send-button {
        width: 64px;
        height: 64px;

        i {
          font-size: 56px;
        }
      }
    }
  }
}
</style>
