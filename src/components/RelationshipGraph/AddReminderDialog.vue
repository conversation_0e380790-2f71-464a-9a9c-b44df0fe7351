<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container add-dialog">
      <div class="dialog-header">
        <div class="dialog-title">添加提醒</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content add-content">
        <!-- 自然语言添加区域 -->
        <div class="natural-add-section">
          <!-- 输入提示 -->
          <div class="input-hint">
            <div class="hint-title">自然语言添加</div>
            <div class="hint-desc">用自然语言描述您的提醒需求，例如：</div>
            <div class="hint-examples">
              <div class="example-item">• 明天上午9点开会，提前半小时提醒我</div>
              <div class="example-item">• 每周五下午5点提醒我写周报</div>
              <div class="example-item">• 下个月5号是妈妈生日，提前一天提醒</div>
            </div>
          </div>

          <!-- 文字输入框 -->
          <div class="input-group">
            <label class="input-label">提醒内容</label>
            <textarea
              v-model="inputText"
              class="input-field natural-input"
              placeholder="请用自然语言描述您的提醒需求..."
              maxlength="200"
              rows="3"
              :disabled="isRecording"
            ></textarea>
            <div class="char-count">{{ inputText.length }}/200</div>
          </div>

          <!-- 语音输入时的文字显示区域 -->
          <div v-if="isRecording" class="voice-text-display">
            <div v-if="!recognizedText" class="voice-placeholder">我在听，请说...</div>
            <div v-else class="voice-message-text">{{ recognizedText }}</div>
          </div>

          <!-- 语音输入区域 -->
          <div class="voice-input-area">
            <!-- 默认状态：左侧语音按钮 + 右侧发送按钮 -->
            <div v-if="!isRecording" class="voice-input-default">
              <button class="voice-mic-btn" @click="startVoiceRecording">
                <div class="voice-button-bg"></div>
                <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
              </button>

              <button
                class="send-btn"
                :class="{ 'not-input': !inputText.trim() || isSubmitting }"
                :disabled="!inputText.trim() || isSubmitting"
                @click="handleSubmit"
              >
                <span v-if="isSubmitting">创建中...</span>
                <span v-else>创建提醒</span>
              </button>
            </div>

            <!-- 录音状态：录音按钮 + 取消按钮 -->
            <div v-else class="voice-input-recording">
              <div class="recording-mic-container">
                <button class="recording-mic-btn" @click="stopVoiceRecording">
                  <div class="voice-button-bg recording"></div>
                  <img src="@/assets/icon/mic.png" alt="录音中" class="recording-mic-icon" />
                </button>
              </div>

              <button class="cancel-recording-btn" @click="cancelRecording">取消录音</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from 'vue';
import { addReminderNatural, type IAddReminderNaturalRequest } from '@/apis/memory';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personId?: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 状态管理
const inputText = ref('');
const isSubmitting = ref(false);

// 语音输入相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const audioBufferIndex = ref(0);
const sessionId = ref('');
const lastBuffer = ref<ArrayBuffer | null>(null);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 重置状态
const resetState = () => {
  inputText.value = '';
  recognizedText.value = '';
  isSubmitting.value = false;
  if (isRecording.value) {
    cancelRecording();
  }
  releaseMicrophoneResources();
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
  recognizedText.value = '';
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，取消录音
    cancelRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    inputText.value = recognizedText.value;
    recognizedText.value = '';
    showToast('语音识别完成');
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理提交
const handleSubmit = async () => {
  if (!inputText.value.trim() || isSubmitting.value) {
    return;
  }

  const content = inputText.value.trim();
  await handleSubmitReminder(content);
};

// 提交提醒
const handleSubmitReminder = async (naturalText: string) => {
  if (!naturalText.trim() || isSubmitting.value) return;

  console.log('🚀 [AddReminderDialog] 开始提交自然语言提醒:', naturalText);

  try {
    isSubmitting.value = true;

    // 构建请求参数
    const requestData: IAddReminderNaturalRequest = {
      user_id: props.userId,
      natural_text: naturalText,
    };

    console.log('📤 [AddReminderDialog] 自然语言提醒请求参数:', requestData);

    // 调用自然语言添加提醒API
    const response = await addReminderNatural(requestData);

    console.log('� [AddReminderDialog] 自然语言提醒响应:', response);

    if (response && response.success) {
      console.log('✅ [AddReminderDialog] 提醒添加成功');
      showSuccessToast('提醒添加成功！');
      emit('success');
      handleClose();
    } else {
      console.warn('⚠️ [AddReminderDialog] 添加提醒失败:', response);
      showFailToast((response?.message as string) || '添加提醒失败');
    }
  } catch (error) {
    console.error('❌ [AddReminderDialog] 提交提醒失败:', error);
    showFailToast('网络错误，添加提醒失败');
  } finally {
    isSubmitting.value = false;
  }
};

// 监听show变化，重置状态
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      resetState();
    }
  },
);

// 处理关闭
const handleClose = () => {
  if (isSubmitting.value) return;

  resetState();
  emit('close');
};

// 组件卸载时清理
onBeforeUnmount(() => {
  resetState();
});
</script>

<style scoped lang="scss">
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;

  &.add-dialog {
    max-height: 90vh;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 36px; // 增加4px (原来32px)
    font-weight: 600;
    flex-shrink: 0;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  overflow-y: auto;

  &.add-content {
    padding-top: 0;
  }
}

// 自然语言添加区域
.natural-add-section {
  .input-hint {
    background: rgba(0, 188, 212, 0.05);
    backdrop-filter: blur(10px);
    border-left: 4px solid #00ffff;
    box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    max-height: 600px;
    overflow-y: auto;

    .hint-title {
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 600;
      margin-bottom: 12px;
    }

    .hint-desc {
      color: rgba(255, 255, 255, 0.9);
      font-size: 28px; // 增加4px (原来24px)
      margin-bottom: 16px;
    }

    .hint-examples {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .example-item {
        color: rgba(0, 188, 212, 0.9);
        font-size: 28px; // 增加4px (原来22px)
      }
    }
  }

  // 语音识别文字显示
  .voice-text-display {
    background: rgba(0, 188, 212, 0.1);
    border: 2px solid #00bcd4;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: rgba(255, 255, 255, 0.6);
      font-size: 28px; // 增加4px (原来24px)
      font-style: italic;
    }

    .voice-message-text {
      color: white;
      font-size: 28px; // 增加4px (原来24px)
      line-height: 1.5;
      text-align: center;
    }
  }

  // 输入组
  .input-group {
    margin-bottom: 32px;
    position: relative;

    .input-label {
      display: block;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 500;
      margin-bottom: 12px;
    }

    .input-field {
      width: 100%;
      height: 300px;
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 16px 16px;
      color: white;
      font-size: 28px; // 增加4px (原来26px)
      outline: none;
      transition: all 0.3s ease;
      box-sizing: border-box;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        border-color: #00bcd4;
        box-shadow: 0 0 0 4px rgba(0, 188, 212, 0.2);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &.natural-input {
        resize: vertical;
        min-height: 120px;
        line-height: 1.5;
      }
    }

    .char-count {
      position: absolute;
      bottom: 12px;
      right: 16px;
      color: rgba(255, 255, 255, 0.5);
      font-size: 24px; // 增加4px (原来20px)
      pointer-events: none;
    }
  }

  // 语音输入区域
  .voice-input-area {
    margin-top: 32px;

    // 默认状态
    .voice-input-default {
      display: flex;
      align-items: center;
      gap: 20px;

      .voice-mic-btn {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        border: 3px solid #00bcd4;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        .voice-button-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: transparent;
          transition: all 0.3s ease;
        }

        .voice-mic-icon {
          width: 32px;
          height: 32px;
          position: relative;
          z-index: 1;
          filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
            contrast(96%);
        }
      }

      .send-btn {
        flex: 1;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 32px; // 增加4px (原来28px)
        font-weight: 600;
        cursor: pointer;
        border: 2px solid #00bcd4;
        background: transparent;
        color: #00bcd4;
        transition: all 0.3s ease;
        min-width: 80px;

        &:hover:not(.not-input) {
          background: rgba(0, 188, 212, 0.1);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
        }

        &.not-input {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }

    // 录音状态
    .voice-input-recording {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 32px;

      .recording-mic-container {
        .recording-mic-btn {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: rgba(255, 68, 68, 0.1);
          border: 3px solid #ff4444;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &:hover {
            background: rgba(255, 68, 68, 0.2);
            transform: scale(1.05);
          }

          .voice-button-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 68, 68, 0.2);
            animation: pulse 1.5s infinite;

            &.recording {
              animation: voiceRecording 2s infinite;
            }
          }

          .recording-mic-icon {
            width: 48px;
            height: 48px;
            position: relative;
            z-index: 1;
            filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg)
              brightness(104%) contrast(97%);
          }
        }
      }

      .cancel-recording-btn {
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.5);
        border-radius: 40px;
        padding: 16px 32px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px; // 增加4px (原来24px)
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #ff4444;
          color: #ff4444;
          background: rgba(255, 68, 68, 0.1);
        }
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}

@keyframes voiceRecording {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(255, 68, 68, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 20px rgba(255, 68, 68, 0);
  }
}
</style>
