// 导入默认头像
import defaultAvatar from '@/assets/icon/user_avatar.png';

// 新的默认头像URL列表
const defaultAvatarUrls = [
  'https://s3plus.meituan.net/aigc-media-resources/relationship/grandma3.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/daughter2.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/grandpa3.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/midagemale4.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E4%B8%AD%E5%B9%B4%E5%A5%B3.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/male3.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E9%9D%92%E5%B9%B4%E5%A5%B3.png',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/boy3.png',
];

// 默认头像映射
const defaultAvatarMap: Record<string, string> = {
  default_avatar_1: defaultAvatarUrls[0],
  default_avatar_2: defaultAvatarUrls[1],
  default_avatar_3: defaultAvatarUrls[2],
  default_avatar_4: defaultAvatarUrls[3],
  default_avatar_5: defaultAvatarUrls[4],
  default_avatar_6: defaultAvatarUrls[5],
  default_avatar_7: defaultAvatarUrls[6],
  default_avatar_8: defaultAvatarUrls[7],
};

/**
 * 根据人员ID获取随机默认头像ID
 * @param personId 人员ID，用于确保同一人员总是获得相同的头像
 * @returns 默认头像ID
 */
export function getRandomDefaultAvatarId(personId: string): string {
  const avatarIds = Object.keys(defaultAvatarMap);
  // 使用人员ID的简单哈希值来确保同一人员总是获得相同的头像
  let hash = 0;
  for (let i = 0; i < personId.length; i++) {
    const char = personId.charCodeAt(i);
    hash = (hash * 31 + char) % 1000000; // 使用简单的乘法和取模运算
  }
  const index = Math.abs(hash) % avatarIds.length;
  return avatarIds[index];
}

/**
 * 根据头像标识符获取头像URL
 * @param avatarId 头像标识符，可能是默认头像ID或自定义头像URL
 * @param personId 人员ID，当avatarId为空时用于生成随机默认头像
 * @returns 头像URL
 */
export function getAvatarUrl(avatarId: string | null | undefined, personId?: string): string {
  if (!avatarId) {
    // 如果没有头像且有人员ID，返回随机默认头像
    if (personId) {
      const randomAvatarId = getRandomDefaultAvatarId(personId);
      return defaultAvatarMap[randomAvatarId];
    }
    return defaultAvatar;
  }

  // 如果是默认头像ID，返回对应的本地图片
  if (defaultAvatarMap[avatarId]) {
    return defaultAvatarMap[avatarId];
  }

  // 如果是自定义头像URL，直接返回
  if (avatarId.startsWith('http') || avatarId.startsWith('data:') || avatarId.startsWith('blob:')) {
    return avatarId;
  }

  // 其他情况返回默认头像
  return defaultAvatar;
}

/**
 * 检查是否为默认头像
 * @param avatarId 头像标识符
 * @returns 是否为默认头像
 */
export function isDefaultAvatar(avatarId: string | null | undefined): boolean {
  return !!(avatarId && defaultAvatarMap[avatarId]);
}

/**
 * 获取所有默认头像列表
 * @returns 默认头像列表
 */
export function getDefaultAvatars() {
  return [
    { id: 'default_avatar_1', src: defaultAvatarUrls[0] },
    { id: 'default_avatar_2', src: defaultAvatarUrls[1] },
    { id: 'default_avatar_3', src: defaultAvatarUrls[2] },
    { id: 'default_avatar_4', src: defaultAvatarUrls[3] },
    { id: 'default_avatar_5', src: defaultAvatarUrls[4] },
    { id: 'default_avatar_6', src: defaultAvatarUrls[5] },
    { id: 'default_avatar_7', src: defaultAvatarUrls[6] },
    { id: 'default_avatar_8', src: defaultAvatarUrls[7] },
  ];
}
