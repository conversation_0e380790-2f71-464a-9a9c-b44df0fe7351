import fetchInstance from '@/lib/fetch';
import { flattenKeyAttributes } from './relation';

// 事件记忆接口定义
export interface IEvent {
  event_id: string;
  user_id: string;
  description_text: string;
  timestamp: string;
  participants: string[];
  location: string;
  topics: string[];
}

// 获取短期记忆响应接口
export interface IMemoryEventsResponse {
  result: string;
  events: IEvent[];
}

// 获取短期记忆（事件记忆）
export async function getMemoryEvents(userId: string, size: number = 100): Promise<IMemoryEventsResponse> {
  return fetchInstance.fetch('/humanrelation/get_all_short_memory_by_user_id', {
    method: 'GET',
    params: {
      user_id: userId,
      size,
    },
  });
}

// 获取用户与特定人员之间的记忆接口
export interface IPersonMemoryRequest {
  user_id: string;
  person_id: string;
}

// 获取用户与特定人员之间的记忆响应接口
export interface IPersonMemoryResponse {
  result: string;
  events: IEvent[];
}

// 获取用户与特定人员之间的记忆
export async function getPersonMemories(params: IPersonMemoryRequest): Promise<IPersonMemoryResponse> {
  console.log('📤 [memory.ts] getPersonMemories API调用开始:', {
    url: '/humanrelation/search_memory_by_person_id',
    method: 'POST',
    params,
  });

  const requestBody = {
    user_id: params.user_id,
    person_id: params.person_id,
  };
  console.log('📤 [memory.ts] 请求体:', requestBody);

  try {
    const response = await fetchInstance.fetch('/humanrelation/search_memory_by_person_id', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] API调用失败:', error);
    throw error;
  }
}

// 更新事件请求接口
export interface IUpdateEventRequest {
  user_id: string;
  event_id: string;
  description_text?: string;
  participants?: string[];
  location?: string;
  topics?: string[];
}

// 更新事件响应接口
export interface IUpdateEventResponse {
  success: boolean;
  message: string;
  event_id: string;
  updated_fields: string[];
}

// 更新事件
export async function updateEvent(params: IUpdateEventRequest): Promise<IUpdateEventResponse> {
  console.log('📤 [memory.ts] updateEvent API调用开始:', {
    url: '/humanrelation/update_event',
    method: 'PUT',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/update_event', {
      method: 'PUT',
      body: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 更新事件API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 更新事件API调用失败:', error);
    throw error;
  }
}

// 添加事件请求接口
export interface IAddEventRequest {
  user_id: string;
  description_text?: string;
  participants?: string[];
  location?: string;
  topics?: string[];
}

// 添加事件响应接口
export interface IAddEventResponse {
  result: string;
  event_id?: string;
  reason?: string;
}

// 添加事件
export async function addPersonEvent(params: IAddEventRequest): Promise<IAddEventResponse> {
  console.log('📤 [memory.ts] addPersonEvent API调用开始:', {
    url: '/humanrelation/add_person_event',
    method: 'POST',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/add_person_event', {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 添加事件API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 添加事件API调用失败:', error);
    throw error;
  }
}

// 删除事件请求接口
export interface IDeleteEventRequest {
  user_id: string;
  event_id: string;
}

// 删除事件响应接口
export interface IDeleteEventResponse {
  result: string;
  event_id?: string;
  reason?: string;
}

// 删除事件
export async function deletePersonEvent(params: IDeleteEventRequest): Promise<IDeleteEventResponse> {
  console.log('📤 [memory.ts] deletePersonEvent API调用开始:', {
    url: '/humanrelation/delete_person_event',
    method: 'DELETE',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/delete_person_event', {
      method: 'DELETE',
      body: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 删除事件API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 删除事件API调用失败:', error);
    throw error;
  }
}

// 获取人员详情请求接口
export interface IGetPersonDetailRequest {
  user_id: string;
  person_id: string;
}

// 人员详情数据接口
export interface IPersonDetail {
  person_id: string;
  user_id: string;
  canonical_name: string;
  aliases: string;
  relationships: string[] | string; // 支持字符串和数组两种格式
  profile_summary: string;
  key_attributes: Record<string, string> | string; // 支持字符串和对象两种格式
  avatar: string;
  is_user: boolean;
}

// 获取人员详情响应接口
export interface IGetPersonDetailResponse {
  result: string;
  person: IPersonDetail;
}

// 获取人员详情
export async function getPersonDetail(params: IGetPersonDetailRequest): Promise<IGetPersonDetailResponse> {
  console.log('📤 [memory.ts] getPersonDetail API调用开始:', {
    url: `/humanrelation/person/${params.person_id}`,
    method: 'GET',
    params,
  });

  try {
    const response = await fetchInstance.fetch(`/humanrelation/person/${params.person_id}`, {
      method: 'GET',
      params: {
        user_id: params.user_id,
      },
    });

    console.log('📡 [memory.ts] 获取人员详情API响应:', response);

    // 处理key_attributes和relationships字段，如果是字符串则解析为对象/数组
    if (response && response.person) {
      // 处理key_attributes字段 - 使用扁平化函数
      if (response.person.key_attributes) {
        try {
          response.person.key_attributes = flattenKeyAttributes(response.person.key_attributes);
          console.log('✅ [memory.ts] key_attributes扁平化成功:', response.person.key_attributes);
        } catch (parseError) {
          console.warn('⚠️ [memory.ts] key_attributes扁平化失败，使用空对象:', parseError);
          response.person.key_attributes = {};
        }
      }

      // 处理relationships字段
      if (response.person.relationships && typeof response.person.relationships === 'string') {
        try {
          response.person.relationships = JSON.parse(response.person.relationships as string);
          console.log('✅ [memory.ts] relationships JSON解析成功:', response.person.relationships);
        } catch (parseError) {
          console.warn('⚠️ [memory.ts] relationships JSON解析失败，使用空数组:', parseError);
          response.person.relationships = [];
        }
      }
    }

    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取人员详情API调用失败:', error);
    throw error;
  }
}

// 获取人员天气请求接口
export interface IGetPersonWeatherRequest {
  user_id: string;
  person_id: string;
}

// 天气预报数据接口
export interface IWeatherForecast {
  date: string;
  tempMax: string;
  tempMin: string;
  textDay: string;
  textNight: string;
  iconDay: string;
  iconNight: string;
  windDirDay: string;
  windDirNight: string;
  windScaleDay: string;
  windScaleNight: string;
  windSpeedDay: string;
  windSpeedNight: string;
  wind360Day: string;
  wind360Night: string;
  humidity: string;
  pressure: string;
  visibility: string;
  precipitation: string;
  uvIndex: string;
  cloud: string;
  sunrise: string;
  sunset: string;
  moonrise: string;
  moonset: string;
  moonPhase: string;
  moonPhaseIcon: string;
  raw_data: Record<string, unknown>;
}

// 逐小时天气数据接口
export interface IWeatherHourly {
  fxTime: string;
  temp: string;
  feelsLike: string;
  text: string;
  icon: string;
  windDir: string;
  windScale: string;
  windSpeed: string;
  wind360: string;
  humidity: string;
  pressure: string;
  precipitation: string;
  pop: string;
  cloud: string;
  dewPoint: string;
  raw_data: Record<string, unknown>;
}

// 详细天气数据接口
export interface IDetailedWeatherData {
  temperature: string;
  feelsLike: string;
  weather: string;
  humidity: string;
  windDirection: string;
  windSpeed: string;
  windPower: string;
  pressure: string;
  visibility: string;
  precipitation: string;
  cloud: string;
  dewPoint: string;
  updateTime: string;
  forecast: IWeatherForecast[];
  hourly: IWeatherHourly[];
}

// 城市天气数据接口
export interface ICityWeatherData {
  city: string;
  weather_data: IDetailedWeatherData;
  source: string;
  personalized_reminder: string;
}

// 获取人员天气响应接口
export interface IGetPersonWeatherResponse {
  result: string;
  locations?: string[];
  weather_data?: Record<string, ICityWeatherData>;
  reason?: string;
  suggestion?: string;
}

// 获取人员天气
export async function getPersonWeather(params: IGetPersonWeatherRequest): Promise<IGetPersonWeatherResponse> {
  try {
    const requestBody: IGetPersonWeatherRequest = {
      user_id: params.user_id,
      person_id: params.person_id,
    };

    const response = await fetchInstance.fetch('/humanrelation/get_weather', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取人员天气API调用失败:', error);
    throw error;
  }
}

// 提醒相关接口定义

// 提醒数据接口
export interface IReminder {
  reminder_id: number;
  user_id: string;
  subject_person_id?: string;
  reminder_text_template?: string;
  display_text?: string;
  base_event_date: string;
  next_trigger_time: string;
  advance_notice_config?: Record<string, unknown>;
  recurrence_rule?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// 获取提醒列表请求接口
export interface IGetRemindersRequest {
  user_id: string;
}

// 获取提醒列表响应接口
export interface IGetRemindersResponse {
  success: boolean;
  reminders: IReminder[];
  count: number;
}

// 获取提醒列表
export async function getReminders(params: IGetRemindersRequest): Promise<IGetRemindersResponse> {
  console.log('📤 [memory.ts] getReminders API调用开始:', {
    url: '/humanrelation/list_reminders',
    method: 'GET',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/list_reminders', {
      method: 'GET',
      params: {
        user_id: params.user_id,
      },
    });

    console.log('📡 [memory.ts] 获取提醒列表API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取提醒列表API调用失败:', error);
    throw error;
  }
}

// 添加提醒请求接口
export interface IAddReminderRequest {
  user_id: string;
  base_event_date: string;
  next_trigger_time: string;
  subject_person_id?: string;
  reminder_text_template?: string;
  advance_notice_config?: Record<string, unknown>;
  recurrence_rule?: string;
  status?: string;
}

// 添加提醒响应接口
export interface IAddReminderResponse {
  success: boolean;
  reminder_id?: string;
  message?: string;
}

// 添加提醒
export async function addReminder(params: IAddReminderRequest): Promise<IAddReminderResponse> {
  console.log('📤 [memory.ts] addReminder API调用开始:', {
    url: '/humanrelation/add_reminder',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      user_id: params.user_id,
      base_event_date: params.base_event_date,
      next_trigger_time: params.next_trigger_time,
      subject_person_id: params.subject_person_id,
      reminder_text_template: params.reminder_text_template,
      advance_notice_config: params.advance_notice_config || {},
      recurrence_rule: params.recurrence_rule,
      status: params.status || 'active',
    };

    const response = await fetchInstance.fetch('/humanrelation/add_reminder', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 添加提醒API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 添加提醒API调用失败:', error);
    throw error;
  }
}

// 删除提醒请求接口
export interface IDeleteReminderRequest {
  user_id: string;
  reminder_id: number;
}

// 删除提醒响应接口
export interface IDeleteReminderResponse {
  success: boolean;
  message: string;
}

// 删除提醒
export async function deleteReminder(params: IDeleteReminderRequest): Promise<IDeleteReminderResponse> {
  console.log('📤 [memory.ts] deleteReminder API调用开始:', {
    url: '/humanrelation/delete_reminder',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      user_id: params.user_id,
      reminder_id: params.reminder_id,
    };

    const response = await fetchInstance.fetch('/humanrelation/delete_reminder', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 删除提醒API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 删除提醒API调用失败:', error);
    throw error;
  }
}

// 修改提醒请求接口
export interface IUpdateReminderRequest {
  user_id: string;
  reminder_id: number;
  base_event_date: string;
  next_trigger_time: string;
  subject_person_id?: string;
  reminder_text_template?: string;
  advance_notice_config?: Record<string, unknown>;
  recurrence_rule?: string;
  status?: string;
}

// 修改提醒响应接口
export interface IUpdateReminderResponse {
  result: string;
  message?: string;
}

// 修改提醒
export async function updateReminder(params: IUpdateReminderRequest): Promise<IUpdateReminderResponse> {
  console.log('📤 [memory.ts] updateReminder API调用开始:', {
    url: '/humanrelation/update_reminder',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      user_id: params.user_id,
      reminder_id: params.reminder_id,
      base_event_date: params.base_event_date,
      next_trigger_time: params.next_trigger_time,
      subject_person_id: params.subject_person_id,
      reminder_text_template: params.reminder_text_template,
      advance_notice_config: params.advance_notice_config || {},
      recurrence_rule: params.recurrence_rule,
      status: params.status || 'active',
    };

    const response = await fetchInstance.fetch('/humanrelation/update_reminder', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 修改提醒API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 修改提醒API调用失败:', error);
    throw error;
  }
}

// 懂量接口定义
export interface IIntimacyRequest {
  user_id: string;
}

// 懂量响应接口
export interface IIntimacyResponse {
  result: string;
  user_id: string;
  intimacy_score: number;
  level: string;
  next_level: string;
  progress_to_next: number;
  points_needed: number;
}

// 获取懂量信息
export async function getIntimacy(params: IIntimacyRequest): Promise<IIntimacyResponse> {
  console.log('📤 [memory.ts] getIntimacy API调用开始:', {
    url: '/humanrelation/intimacy',
    method: 'GET',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/intimacy', {
      method: 'GET',
      params: {
        user_id: params.user_id,
      },
    });

    console.log('📡 [memory.ts] 获取懂量API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取懂量API调用失败:', error);
    throw error;
  }
}

// 综合天气接口定义
export interface IComprehensiveWeatherRequest {
  user_id: string;
}

// 地点信息接口
export interface ILocationInfo {
  name: string;
  type: string;
  source: string;
  person_name: string;
  relationship: string;
}

// 预报数据接口
export interface IForecastItem {
  date: string;
  tempMax: string;
  tempMin: string;
  textDay: string;
  textNight: string;
  iconDay: string;
  iconNight: string;
  [key: string]: unknown;
}

// 小时数据接口
export interface IHourlyItem {
  time: string;
  temp: string;
  text: string;
  icon: string;
  [key: string]: unknown;
}

// 天气详细信息接口
export interface IWeatherDetail {
  temperature: string;
  feelsLike: string;
  weather: string;
  humidity: string;
  windDirection: string;
  windSpeed: string;
  windPower: string;
  pressure: string;
  visibility: string;
  precipitation: string;
  cloud: string;
  dewPoint: string;
  updateTime: string;
  forecast: IForecastItem[];
  hourly: IHourlyItem[];
}

// 天气数据项接口
export interface IWeatherDataItem {
  location: ILocationInfo;
  weather: IWeatherDetail;
  success: boolean;
  adcode: string;
  source: string;
}

// 天气汇总信息接口
export interface IWeatherSummary {
  total_locations: number;
  successful_queries: number;
  failed_queries: number;
}

// 综合天气响应接口
export interface IComprehensiveWeatherResponse {
  result: string;
  user_id: string;
  timestamp: string;
  weather_summary: IWeatherSummary;
  locations: ILocationInfo[];
  weather_data: IWeatherDataItem[];
  ai_reminder: string;
}

// 获取综合天气信息
export async function getComprehensiveWeather(
  params: IComprehensiveWeatherRequest,
): Promise<IComprehensiveWeatherResponse> {
  console.log('📤 [memory.ts] getComprehensiveWeather API调用开始:', {
    url: '/humanrelation/comprehensive_weather',
    method: 'GET',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/comprehensive_weather', {
      method: 'GET',
      params: {
        user_id: params.user_id,
      },
    });

    console.log('📡 [memory.ts] 获取综合天气API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取综合天气API调用失败:', error);
    throw error;
  }
}

// 获取与天气相关的人物档案和事件请求接口
export interface IGetWeatherRelatedInfoRequest {
  user_id: string;
}

// 与天气相关的人物信息
export interface IWeatherRelatedPerson {
  person_id: string;
  canonical_name: string;
  relationship: string;
  profile_summary: string;
  location_info: Record<string, string>;
}

// 与天气相关的事件信息
export interface IWeatherRelatedEvent {
  event_id: string;
  description_text: string;
  location: string;
  timestamp: string;
  participants: string[];
}

// 获取与天气相关的人物档案和事件响应接口
export interface IGetWeatherRelatedInfoResponse {
  result: 'success' | 'error';
  user_id?: string;
  timestamp?: string;
  weather_related_persons?: IWeatherRelatedPerson[];
  weather_related_events?: IWeatherRelatedEvent[];
  summary?: {
    total_persons: number;
    total_events: number;
  };
  reason?: string; // 错误时的原因
}

// 获取与天气相关的人物档案和事件
export async function getWeatherRelatedInfo(
  params: IGetWeatherRelatedInfoRequest,
): Promise<IGetWeatherRelatedInfoResponse> {
  console.log('📤 [memory.ts] getWeatherRelatedInfo API调用开始:', {
    url: '/humanrelation/get_weather_related_info',
    method: 'GET',
    params,
  });

  try {
    const response = await fetchInstance.fetch('/humanrelation/get_weather_related_info', {
      method: 'GET',
      params: {
        user_id: params.user_id,
      },
    });

    console.log('📡 [memory.ts] 获取天气相关信息API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取天气相关信息API调用失败:', error);
    throw error;
  }
}

// 推荐话题接口定义
export interface IRecommendTopicsRequest {
  user_id: string;
  person_id: string;
  max_topics?: number; // 最大推荐话题数量，默认2个，范围1-5
  fast_mode?: boolean; // 快速模式，跳过AI调用直接返回备用话题（可选）
}

// 推荐话题数据接口
export interface IRecommendedTopic {
  topic: string;
  category: string;
  description: string;
  priority: number;
}

// 推荐话题响应接口
export interface IRecommendTopicsResponse {
  result: string;
  user_id: string;
  person_id: string;
  recommended_topics: IRecommendedTopic[];
  context_summary: {
    user_profile_available: boolean;
    events_count: number;
    has_reminders: boolean;
  };
  generated_at: string;
}

// 获取推荐话题
export async function getRecommendTopics(params: IRecommendTopicsRequest): Promise<IRecommendTopicsResponse> {
  console.log('📤 [memory.ts] getRecommendTopics API调用开始:', {
    url: '/humanrelation/recommend_topics',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      user_id: params.user_id,
      person_id: params.person_id,
      max_topics: params.max_topics || 2,
      fast_mode: params.fast_mode || false,
    };

    const response = await fetchInstance.fetch('/humanrelation/recommend_topics', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 获取推荐话题API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 获取推荐话题API调用失败:', error);
    throw error;
  }
}

// 自然语言更新提醒请求接口
export interface IUpdateReminderNaturalRequest {
  user_id: string;
  reminder_id: number;
  natural_text: string;
}

// 自然语言更新提醒响应接口
export interface IUpdateReminderNaturalResponse {
  result: 'success' | 'error';
  message?: string;
}

// 自然语言更新提醒
export async function updateReminderNatural(
  params: IUpdateReminderNaturalRequest,
): Promise<IUpdateReminderNaturalResponse> {
  console.log('📤 [memory.ts] updateReminderNatural API调用开始:', {
    url: '/humanrelation/update_reminder_natural',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      user_id: params.user_id,
      reminder_id: params.reminder_id,
      natural_text: params.natural_text,
    };

    const response = await fetchInstance.fetch('/humanrelation/update_reminder_natural', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 自然语言更新提醒API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 自然语言更新提醒API调用失败:', error);
    throw error;
  }
}

// 自然语言添加提醒请求接口
export interface IAddReminderNaturalRequest {
  user_id: string;
  natural_text: string;
}

// 自然语言添加提醒响应接口
export interface IAddReminderNaturalResponse {
  success: boolean;
  reminder_id?: string;
  message?: string;
}

// 自然语言添加提醒
export async function addReminderNatural(params: IAddReminderNaturalRequest): Promise<IAddReminderNaturalResponse> {
  console.log('📤 [memory.ts] addReminderNatural API调用开始:', {
    url: '/humanrelation/add_reminder_natural',
    method: 'POST',
    params,
  });

  try {
    const requestBody = {
      user_id: params.user_id,
      natural_text: params.natural_text,
    };

    const response = await fetchInstance.fetch('/humanrelation/add_reminder_natural', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [memory.ts] 自然语言添加提醒API响应:', response);
    return response;
  } catch (error) {
    console.error('❌ [memory.ts] 自然语言添加提醒API调用失败:', error);
    throw error;
  }
}
